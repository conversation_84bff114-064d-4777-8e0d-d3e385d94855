# If needed:
# !pip install matminer pymatgen

import pandas as pd
from matminer.datasets import load_dataset
from pymatgen.core import Structure

# 1) Load the dataset
# This returns a pandas DataFrame. In most matminer versions, the "structure"
# column is already pymatgen Structure objects.
df = load_dataset("matbench_perovskites")
print(f"Loaded matbench_perovskites with shape: {df.shape}")
print(df.columns.tolist())

# 2) Ensure the 'structure' column contains pymatgen Structure objects
def ensure_pmg_structure(x):
    if isinstance(x, Structure):
        return x
    if isinstance(x, dict) and x.get("@module", "").startswith("pymatgen"):
        return Structure.from_dict(x)
    raise TypeError(f"Unexpected structure format: {type(x)}")

if "structure" not in df.columns:
    raise KeyError("The dataset does not contain a 'structure' column. Check your matminer version.")

df["structure"] = df["structure"].apply(ensure_pmg_structure)

# 3) (Optional) Keep only essential columns for modeling
# Many workflows just need an ID, the structure, and the target (e.g., 'e_form').
# The exact target column name may vary by matminer version; print columns above to confirm.
possible_targets = ["e_form", "formation_energy_per_atom", "target", "e_form_target"]
target_col = next((c for c in possible_targets if c in df.columns), None)

cols_to_keep = ["structure"] + ([target_col] if target_col else [])
df_pmg = df[cols_to_keep].copy() if cols_to_keep else df.copy()

print("Preview:")
print(df_pmg.head(3))
print("Types in 'structure' column:", type(df_pmg["structure"].iloc[0]))
print("Target column:", target_col)