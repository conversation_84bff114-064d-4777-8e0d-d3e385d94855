#!/usr/bin/env python
"""
Dataset preparation script for matbench_perovskites SOAP featurization.
Supports chunking the dataset for parallel processing.
"""

import argparse
import os
import pandas as pd
from matminer.datasets import load_dataset
from pymatgen.core import Structure
import numpy as np


def ensure_pmg_structure(x):
    """Ensure structure is a pymatgen Structure object."""
    if isinstance(x, Structure):
        return x
    if isinstance(x, dict) and x.get("@module", "").startswith("pymatgen"):
        return Structure.from_dict(x)
    raise TypeError(f"Unexpected structure format: {type(x)}")


def load_and_prepare_dataset():
    """Load and prepare the matbench_perovskites dataset."""
    print("Loading matbench_perovskites dataset...")
    df = load_dataset("matbench_perovskites")
    print(f"Loaded matbench_perovskites with shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")

    # Ensure the 'structure' column contains pymatgen Structure objects
    if "structure" not in df.columns:
        raise KeyError("The dataset does not contain a 'structure' column. Check your matminer version.")

    print("Converting structures to pymatgen format...")
    df["structure"] = df["structure"].apply(ensure_pmg_structure)

    # Find the target column
    possible_targets = ["e_form", "formation_energy_per_atom", "target", "e_form_target"]
    target_col = next((c for c in possible_targets if c in df.columns), None)

    if target_col is None:
        raise ValueError(f"No target column found. Available columns: {df.columns.tolist()}")

    print(f"Using target column: {target_col}")

    # Create material_id if not present
    if "material_id" not in df.columns:
        df["material_id"] = [f"perovskite_{i:06d}" for i in range(len(df))]
        print("Created material_id column")

    # Keep essential columns and rename target to 'e_form' for consistency
    essential_cols = ["material_id", "structure", target_col]
    df_clean = df[essential_cols].copy()
    if target_col != "e_form":
        df_clean = df_clean.rename(columns={target_col: "e_form"})

    print(f"Final dataset shape: {df_clean.shape}")
    print("Preview:")
    print(df_clean.head(3))
    print(f"Structure type: {type(df_clean['structure'].iloc[0])}")

    return df_clean


def create_chunks(df, chunk_size, output_dir="chunks"):
    """Split dataset into chunks for parallel processing."""
    os.makedirs(output_dir, exist_ok=True)

    n_samples = len(df)
    n_chunks = int(np.ceil(n_samples / chunk_size))

    print(f"Creating {n_chunks} chunks of size {chunk_size} from {n_samples} samples")

    chunk_info = []

    for i in range(n_chunks):
        start_idx = i * chunk_size
        end_idx = min((i + 1) * chunk_size, n_samples)

        chunk_df = df.iloc[start_idx:end_idx].copy()
        chunk_filename = f"chunk_{i:04d}.pkl"
        chunk_path = os.path.join(output_dir, chunk_filename)

        # Save as pickle to preserve pymatgen Structure objects
        chunk_df.to_pickle(chunk_path)

        chunk_info.append({
            'chunk_id': i,
            'filename': chunk_filename,
            'start_idx': start_idx,
            'end_idx': end_idx,
            'n_samples': len(chunk_df),
            'path': chunk_path
        })

        print(f"Created chunk {i:04d}: samples {start_idx}-{end_idx-1} ({len(chunk_df)} samples)")

    # Save chunk information
    chunk_info_df = pd.DataFrame(chunk_info)
    info_path = os.path.join(output_dir, "chunk_info.csv")
    chunk_info_df.to_csv(info_path, index=False)
    print(f"Saved chunk information to {info_path}")

    return chunk_info_df


def main():
    parser = argparse.ArgumentParser(description="Prepare matbench_perovskites dataset for SOAP featurization")
    parser.add_argument("--chunk_size", type=int, default=100,
                       help="Number of samples per chunk (default: 100)")
    parser.add_argument("--output_dir", type=str, default="chunks",
                       help="Output directory for chunks (default: chunks)")
    parser.add_argument("--save_full", action="store_true",
                       help="Also save the full dataset as a single file")

    args = parser.parse_args()

    # Load and prepare dataset
    df = load_and_prepare_dataset()

    # Save full dataset if requested
    if args.save_full:
        full_path = "matbench_perovskites_prepared.pkl"
        df.to_pickle(full_path)
        print(f"Saved full dataset to {full_path}")

    # Create chunks
    chunk_info = create_chunks(df, args.chunk_size, args.output_dir)

    print(f"\nDataset preparation complete!")
    print(f"Total samples: {len(df)}")
    print(f"Number of chunks: {len(chunk_info)}")
    print(f"Chunk size: {args.chunk_size}")
    print(f"Output directory: {args.output_dir}")


if __name__ == "__main__":
    main()