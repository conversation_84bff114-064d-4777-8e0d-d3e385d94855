#!/usr/bin/env python
"""
Test script to validate the SOAP featurization setup.
Tests data loading, SOAP initialization, and small-scale featurization.
"""

import sys
import time
import pandas as pd
import numpy as np
from pathlib import Path

# Test imports
try:
    from pymatgen.core import Structure
    from pymatgen.core.periodic_table import Element
    from pymatgen.io.ase import AseAtomsAdaptor
    from matminer.featurizers.structure import SiteStatsFingerprint
    from matminer.datasets import load_dataset
    from dscribe.descriptors import SOAP as DScribeSOAP
    from sklearn.feature_selection import RFE
    from sklearn.ensemble import RandomForestRegressor
    print("✓ All required packages imported successfully")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

# Import our modules
try:
    from soap_featurize_perovskites import (
        MASTER_ELEMENTS, SOAP_CONFIG, DScribeSOAPWrapper, 
        initialize_soap_featurizer, featurize_structures
    )
    print("✓ Local modules imported successfully")
except ImportError as e:
    print(f"✗ Local module import error: {e}")
    sys.exit(1)


def test_data_loading():
    """Test loading matbench_perovskites dataset."""
    print("\n=== Testing Data Loading ===")
    try:
        df = load_dataset("matbench_perovskites")
        print(f"✓ Loaded matbench_perovskites: {df.shape}")
        print(f"  Columns: {df.columns.tolist()}")
        
        # Check structure column
        if "structure" in df.columns:
            sample_structure = df["structure"].iloc[0]
            print(f"  Sample structure type: {type(sample_structure)}")
            if isinstance(sample_structure, Structure):
                print(f"  Structure formula: {sample_structure.composition.reduced_formula}")
                print(f"  Number of sites: {len(sample_structure)}")
            print("✓ Structure column validated")
        else:
            print("✗ No structure column found")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Data loading failed: {e}")
        return False


def test_soap_initialization():
    """Test SOAP featurizer initialization."""
    print("\n=== Testing SOAP Initialization ===")
    try:
        print(f"Master elements count: {len(MASTER_ELEMENTS)}")
        print(f"SOAP config: {SOAP_CONFIG}")
        
        ssf, soap_wrapper = initialize_soap_featurizer()
        print(f"✓ SOAP featurizer initialized")
        print(f"  Expected feature count: {soap_wrapper._nfeat * 2}")
        print(f"  Feature dimensions: S={soap_wrapper._S}, n_max={soap_wrapper._nmax}, l_max={soap_wrapper._lmax}")
        
        return ssf, soap_wrapper
    except Exception as e:
        print(f"✗ SOAP initialization failed: {e}")
        return None, None


def test_small_featurization(ssf):
    """Test featurization on a small sample."""
    print("\n=== Testing Small-Scale Featurization ===")
    try:
        # Load a small sample
        df = load_dataset("matbench_perovskites")
        sample_size = 3
        sample_df = df.head(sample_size)
        
        # Ensure structures are pymatgen objects
        def ensure_structure(x):
            if isinstance(x, Structure):
                return x
            if isinstance(x, dict):
                return Structure.from_dict(x)
            return x
        
        sample_df["structure"] = sample_df["structure"].apply(ensure_structure)
        
        structures = sample_df["structure"].tolist()
        material_ids = [f"test_{i}" for i in range(sample_size)]
        
        print(f"Testing featurization on {sample_size} structures...")
        
        start_time = time.time()
        features_df = featurize_structures(structures, material_ids, ssf)
        end_time = time.time()
        
        print(f"✓ Featurization completed in {end_time - start_time:.2f} seconds")
        print(f"  Result shape: {features_df.shape}")
        print(f"  Feature columns: {len([c for c in features_df.columns if 'SOAP' in c])}")
        
        # Check for NaN values
        nan_count = features_df.isnull().sum().sum()
        if nan_count > 0:
            print(f"⚠ Warning: {nan_count} NaN values found in features")
        else:
            print("✓ No NaN values in features")
        
        return True
    except Exception as e:
        print(f"✗ Small featurization test failed: {e}")
        return False


def test_chunking_functionality():
    """Test data chunking functionality."""
    print("\n=== Testing Chunking Functionality ===")
    try:
        # Import chunking functions
        from get_matbench_perovskites import load_and_prepare_dataset, create_chunks
        
        # Load small dataset
        df = load_and_prepare_dataset()
        print(f"✓ Dataset loaded: {df.shape}")
        
        # Create test chunks
        test_output_dir = "test_chunks"
        chunk_size = 100
        chunk_info = create_chunks(df.head(250), chunk_size, test_output_dir)
        
        print(f"✓ Created {len(chunk_info)} test chunks")
        print(f"  Chunk info shape: {chunk_info.shape}")
        
        # Test loading a chunk
        if len(chunk_info) > 0:
            test_chunk_path = chunk_info.iloc[0]["path"]
            test_chunk = pd.read_pickle(test_chunk_path)
            print(f"✓ Successfully loaded test chunk: {test_chunk.shape}")
        
        return True
    except Exception as e:
        print(f"✗ Chunking test failed: {e}")
        return False


def test_rfe_functionality():
    """Test RFE functionality with dummy data."""
    print("\n=== Testing RFE Functionality ===")
    try:
        from soap_featurize_perovskites import perform_rfe
        
        # Create dummy SOAP features
        n_samples = 100
        n_features = 50
        
        dummy_data = {
            "material_id": [f"dummy_{i}" for i in range(n_samples)],
            "e_form": np.random.normal(0, 1, n_samples)
        }
        
        # Add dummy SOAP features
        for i in range(n_features):
            dummy_data[f"SOAP_feature_{i:03d}"] = np.random.normal(0, 1, n_samples)
        
        dummy_df = pd.DataFrame(dummy_data)
        print(f"Created dummy dataset: {dummy_df.shape}")
        
        # Test RFE
        n_select = 10
        reduced_df, selected_features = perform_rfe(
            dummy_df, target_col="e_form", n_features=n_select
        )
        
        print(f"✓ RFE completed")
        print(f"  Reduced dataset shape: {reduced_df.shape}")
        print(f"  Selected features: {len(selected_features)}")
        
        return True
    except Exception as e:
        print(f"✗ RFE test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=== SOAP Featurization Setup Test ===")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {Path.cwd()}")
    
    tests = [
        ("Data Loading", test_data_loading),
        ("SOAP Initialization", test_soap_initialization),
        ("Chunking Functionality", test_chunking_functionality),
        ("RFE Functionality", test_rfe_functionality),
    ]
    
    results = {}
    ssf = None
    
    for test_name, test_func in tests:
        if test_name == "SOAP Initialization":
            ssf, _ = test_func()
            results[test_name] = ssf is not None
        elif test_name == "Small Featurization" and ssf is not None:
            results[test_name] = test_func(ssf)
        else:
            results[test_name] = test_func()
    
    # Test small featurization if SOAP was initialized successfully
    if ssf is not None:
        results["Small Featurization"] = test_small_featurization(ssf)
    
    # Summary
    print("\n=== Test Summary ===")
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Setup is ready for SOAP featurization.")
    else:
        print("⚠ Some tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
