from pymatgen.core import Structure, Lattice
from pymatgen.core.periodic_table import Element
from pymatgen.io.ase import AseAtomsAdaptor
from matminer.featurizers.structure import SiteStatsFingerprint
import numpy as np
import pandas as pd
import pickle
import random

# dscribe SOAP (not matminer’s wrapper)
from dscribe.descriptors import SOAP as DScribeSOAP

# ----------------------------
# 0) Choose your element scope
# ----------------------------
MASTER_ELEMENTS = [Element.from_Z(Z).symbol for Z in range(1, 119)]
drop = {"He", "Ne", "Ar", "Kr", "Xe", "Rn", "Og"}
MASTER_ELEMENTS = [s for s in MASTER_ELEMENTS if s not in drop]
print(f"Using {len(MASTER_ELEMENTS)} master elements:", MASTER_ELEMENTS[:12], "...")

# -------------------------------------
# 1) Build a periodic template structure
#    (Not strictly needed for dscribe, but fine to keep)
# -------------------------------------
a_template = 5.0
lat_template = Lattice.cubic(a_template)
coords_template = [[random.random(), random.random(), random.random()] for _ in range(len(MASTER_ELEMENTS))]
template = Structure(lat_template, MASTER_ELEMENTS, coords_template, coords_are_cartesian=False)

# -----------------------------------------
# 2) Initialize SOAP (dscribe, periodic=True) with mu1nu1
# -----------------------------------------
rcut = 5.0
nmax = 6
lmax = 6
sigma = 0.5

soap_ds = DScribeSOAP(
    species=MASTER_ELEMENTS,
    r_cut=rcut,
    n_max=nmax,
    l_max=lmax,
    sigma=sigma,
    periodic=True,
    compression={"mode": "mu1nu1"},  # key change
    dtype="float32",                 # lighter memory
)

# -----------------------------------------
# 2b) Wrap dscribe SOAP so SiteStatsFingerprint can call featurize(structure, i)
# -----------------------------------------
class DScribeSOAPWrapper:
    def __init__(self, ds_soap, species):
        self.ds_soap = ds_soap
        self.species = list(species)  # preserve order for labeling
        self._nmax = ds_soap._n_max
        self._lmax = ds_soap._l_max
        self._S = len(self.species)
        # Feature count for mu1nu1 = S * n_max^2 * (l_max+1)
        self._nfeat = self._S * (self._nmax ** 2) * (self._lmax + 1)
        self._ase_adaptor = AseAtomsAdaptor()

    def featurize(self, structure, i):
        # structure: pymatgen Structure, i: site index
        atoms = self._ase_adaptor.get_atoms(structure)
        # dscribe returns shape [n_centers, n_features] for centers=[i]
        x = self.ds_soap.create(atoms, centers=[i])
        return np.asarray(x[0], dtype=np.float32)

    def feature_labels(self):
        # Optional but helpful: stable labels for SiteStatsFingerprint columns
        labels = []
        for z in self.species:
            for l in range(self._lmax + 1):
                for n in range(1, self._nmax + 1):
                    for np_ in range(1, self._nmax + 1):
                        labels.append(f"SOAP_mu1nu1[Z={z}][l={l}][n={n}][np={np_}]")
        return labels

# Instantiate wrapper for matminer aggregator
soap = DScribeSOAPWrapper(soap_ds, MASTER_ELEMENTS)

# Aggregator for structure-level features
ssf = SiteStatsFingerprint(soap, stats=("mean", "std_dev"))

# -------------------------------------
# 3) Example crystal structures to test
# -------------------------------------
a_si = 5.431
silicon = Structure(Lattice.cubic(a_si), ["Si", "Si"], [[0, 0, 0], [0.25, 0.25, 0.25]], coords_are_cartesian=False)

a_nacl = 5.64
nacl = Structure(Lattice.cubic(a_nacl), ["Na", "Cl"], [[0, 0, 0], [0.5, 0.5, 0.5]], coords_are_cartesian=False)

a_mgo = 4.21
mgo = Structure(Lattice.cubic(a_mgo), ["Mg", "O"], [[0, 0, 0], [0.5, 0.5, 0.5]], coords_are_cartesian=False)

# -------------------------------------
# 4) Featurize a list of structures
# -------------------------------------
def featurize_structures(structures):
    df = pd.DataFrame({"structure": structures})
    df_feat = ssf.featurize_dataframe(df, col_id="structure")
    soap_cols = [c for c in df_feat.columns if "SOAP" in c]
    return df_feat[soap_cols]

structures = [silicon, nacl, mgo]
df_features = featurize_structures(structures)
print("Structure-level SOAP features shape:", df_features.shape)
print(df_features.iloc[:2, :8])

# -------------------------------------
# 5) Optional: per-site SOAP for one crystal
# -------------------------------------
site_vecs = np.vstack([soap.featurize(nacl, i) for i in range(len(nacl))])
print("NaCl per-site SOAP matrix shape:", site_vecs.shape)

# -------------------------------------
# 6) Persist the featurizers for reuse
# -------------------------------------
with open("soap_mu1nu1_dscribe.pkl", "wb") as f:
    pickle.dump(soap_ds, f)
with open("ssf_mu1nu1_wrapper.pkl", "wb") as f:
    pickle.dump(soap, f)

# -------------------------------------
# 7) mu1nu1 interpretation helpers
# -------------------------------------
def reshape_mu1nu1(x_flat, species_list, n_max, l_max):
    # x_flat: (features,) or (N, features)
    S = len(species_list)
    L = l_max + 1
    block_size = n_max * n_max * L
    if x_flat.ndim == 1:
        assert x_flat.size == S * block_size
        return x_flat.reshape(S, L, n_max, n_max)
    else:
        N = x_flat.shape[0]
        assert x_flat.shape[1] == S * block_size
        return x_flat.reshape(N, S, L, n_max, n_max)

def summarize_mu1nu1_block(block_Z):
    # block_Z: [L, n_max, n_max] for a single species Z
    per_l = block_Z.sum(axis=(1, 2))                       # length L
    diag_radial = np.trace(block_Z, axis1=1, axis2=2)      # shape [L,]
    total = block_Z.sum()
    diag_total = diag_radial.sum()
    offdiag_total = total - diag_total
    return dict(per_l=per_l, diag_by_l=diag_radial, total=total, diag_total=diag_total, offdiag_total=offdiag_total)

def extract_species_block(x_flat, species_list, target_symbol, n_max, l_max):
    T = reshape_mu1nu1(x_flat, species_list, n_max, l_max)
    z_idx = species_list.index(target_symbol)
    return T[z_idx]  # [L, n_max, n_max]

# Example interpretation on one Na site
example_vec = soap.featurize(nacl, 0)
OBlock = extract_species_block(example_vec, MASTER_ELEMENTS, "Na", nmax, lmax)
summary = summarize_mu1nu1_block(OBlock)
print("Na per-l totals:", summary["per_l"])
print("Na diag_total vs offdiag_total:", summary["diag_total"], summary["offdiag_total"])



### Then we use the functions and featurizer to test in a larger set of structures
import time
import random
from pymatgen.core import Structure, Lattice

# 1) Helper to make a simple cubic perovskite (Pm-3m) with lattice parameter a
def make_perovskite(a, el_a, el_b, el_x):
    lat = Lattice.cubic(a)
    return Structure(
        lat,
        [el_a, el_b, el_x, el_x, el_x],  # A, B, X1, X2, X3
        [
            [0.0, 0.0, 0.0],
            [0.5, 0.5, 0.5],
            [0.5, 0.0, 0.0],
            [0.0, 0.5, 0.0],
            [0.0, 0.0, 0.5],
        ],
        coords_are_cartesian=False,
    )

# 2) Build 100 random cubic perovskites
num_structs = 100
rng = random.Random(42)
elements = MASTER_ELEMENTS[:]

structures = []
for _ in range(num_structs):
    el_a, el_b, el_x = rng.sample(elements, 3)
    a = rng.uniform(3.5, 5.0)
    structures.append(make_perovskite(a, el_a, el_b, el_x))

print(f"Generated {len(structures)} random cubic perovskite crystals.")

# 3) Featurize and time it
def featurize_structures(structures):
    df = pd.DataFrame({"structure": structures})
    df_feat = ssf.featurize_dataframe(df, col_id="structure")
    soap_cols = [c for c in df_feat.columns if "SOAP" in c]
    return df_feat[soap_cols]

t0 = time.perf_counter()
df_features = featurize_structures(structures)
t1 = time.perf_counter()

total_s = t1 - t0
per_struct_s = total_s / len(structures)
throughput = len(structures) / total_s if total_s > 0 else float("inf")

print(f"Structure-level SOAP features shape: {df_features.shape}")
print(f"Total featurization time: {total_s:.3f} s")
print(f"Avg per-structure time: {per_struct_s*1000:.2f} ms/structure")
print(f"Throughput: {throughput:.2f} structures/s")

# Optional: sanity-check per-site feature dimension on one of the generated crystals
sample = structures[0]
X_site = np.vstack([soap.featurize(sample, i) for i in range(len(sample))])
print(f"Sample per-site SOAP matrix shape: {X_site.shape}  # (num_sites, feature_dim)")