#!/usr/bin/env python
"""
Advanced RFE implementation for SOAP features using XGBoost-based selection.
Based on the robust preselection method from context_code_for_rfe.py.
"""

import argparse
import time
import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import <PERSON><PERSON>old
from collections import defaultdict
from sklearn.preprocessing import MinMaxScaler


def time_function(func):
    """Decorator for timing functions."""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        print(f"--- Starting '{func.__name__}' ---")
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"--- Finished '{func.__name__}' in {end_time - start_time:.2f} seconds ---")
        return result
    return wrapper


def rename_cols(df):
    """Make column names XGBoost-compatible by replacing problematic characters."""
    df.columns = [
        col.replace("[", "_").replace("]", "_").replace(" ", "_")
           .replace("<", "_").replace(">", "_").replace(",", "_")
           .replace("|", "_").replace(".", "_")
        for col in df.columns
    ]
    return df


@time_function
def remove_correlated_features(df, threshold=0.98):
    """Remove features that are highly correlated (absolute correlation > threshold)."""
    print(f"Running correlation filter with threshold={threshold} ...")
    corr_matrix = df.corr().abs()
    upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
    to_drop = [column for column in upper.columns if any(upper[column] > threshold)]
    if to_drop:
        print(f" - Dropping {len(to_drop)} highly correlated features before preselection.")
        df = df.drop(columns=to_drop)
    else:
        print(" - No highly correlated features found.")
    return df


@time_function
def xgb_robust_preselection(features_df, targets, target_threshold=300, 
                           n_jobs=24, n_folds=5, consensus_threshold=0.6, 
                           importance_percentile=10):
    """
    Performs robust feature elimination using XGBoost importance ranks across CV folds.
    Stops at exactly target_threshold features.
    """
    cur_features = features_df.copy()
    initial_num = cur_features.shape[1]

    if initial_num <= target_threshold:
        print(f"Feature count ({initial_num}) already at/below target. Skipping selection.")
        return cur_features

    print(f"Starting robust feature elimination from {cur_features.shape[1]} features...")
    print(f"Using {n_folds}-fold CV with {consensus_threshold*100:.0f}% consensus threshold")
    print(f"Features in bottom {importance_percentile}th percentile of ranks are candidates for removal.")

    # Prepare target
    y_numeric = pd.to_numeric(targets, errors='coerce')
    if np.isnan(y_numeric).any():
        print("Warning: Found NaNs in target variable. Removing corresponding samples.")
        valid_idx = ~np.isnan(y_numeric)
        y_numeric = y_numeric[valid_idx]
        cur_features = cur_features.iloc[valid_idx]
    
    print(f"Target variable prepared. Shape: {y_numeric.shape}")

    # Prepare scaler ONCE
    print("Preparing scaler and scaling features...")
    xgb_compatible_features = rename_cols(cur_features.copy())
    X_numeric_temp = xgb_compatible_features.apply(pd.to_numeric, errors='coerce')
    scaler = MinMaxScaler()
    X_scaled = pd.DataFrame(
        scaler.fit_transform(X_numeric_temp),
        columns=X_numeric_temp.columns,
        index=X_numeric_temp.index
    )
    X_numeric_full = X_scaled.fillna(-1)
    print(f"Features scaled and NaNs imputed. Full scaled data shape: {X_numeric_full.shape}")

    # Prepare a timestamped aggregator file for this run
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    dropped_concat_filename = f"dropped_features_soap_rfe_{timestamp}.txt"
    print(f"Logging dropped features to: {dropped_concat_filename}")

    iteration = 0
    while cur_features.shape[1] > target_threshold:
        iteration += 1
        current_num_features = cur_features.shape[1]
        print(f"\nIteration {iteration}: {current_num_features} features remaining.")
        iter_start_time = time.time()

        # Subset scaled data to current features
        X_numeric = X_numeric_full[cur_features.columns]
        print(f"Subsetting data for current iteration. Shape: {X_numeric.shape}")

        feature_rankings = defaultdict(list)
        low_importance_votes = defaultdict(int)

        kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)
        print(f"Starting {n_folds}-fold cross-validation...")

        for fold_idx, (train_idx, val_idx) in enumerate(kf.split(X_numeric)):
            fold_start_time = time.time()
            X_train, y_train = X_numeric.iloc[train_idx], y_numeric.iloc[train_idx]
            print(f"  Fold {fold_idx + 1}/{n_folds}: Training data shape X={X_train.shape}, y={y_train.shape}")

            xgb_model = xgb.XGBRegressor(
                n_jobs=n_jobs,
                random_state=42,
                objective='reg:squarederror',
                n_estimators=100,
                subsample=0.9,
                colsample_bytree=0.9,
                min_child_weight=2,
                max_depth=6
            )

            try:
                model_fit_start = time.time()
                xgb_model.fit(X_train.values, y_train)
                model_fit_end = time.time()
                print(f"  Fold {fold_idx + 1}: XGBoost model fitted in {model_fit_end - model_fit_start:.2f} seconds.")
                
                importances = xgb_model.feature_importances_
                current_ranks = np.argsort(np.argsort(importances))  # 0 = least important, N-1 = most important

                for feature_idx, rank in enumerate(current_ranks):
                    feature_rankings[feature_idx].append(rank)

                rank_threshold = np.percentile(current_ranks, importance_percentile)
                print(f"  Fold {fold_idx + 1}: Rank threshold for removal candidate: {rank_threshold:.2f} (bottom {importance_percentile}th percentile)")
                
                for feature_idx, rank in enumerate(current_ranks):
                    if rank <= rank_threshold:
                        low_importance_votes[feature_idx] += 1
                print(f"  Fold {fold_idx + 1} completed in {time.time() - fold_start_time:.2f} seconds.")

            except Exception as e:
                print(f"  Error in fold {fold_idx + 1}: {e}")
                continue

        # Consensus removal
        print("\nEvaluating consensus for feature removal...")
        features_to_remove = []
        min_votes_needed = int(n_folds * consensus_threshold)
        print(f"Minimum votes needed for consensus: {min_votes_needed} out of {n_folds} folds.")

        for feature_idx in range(current_num_features):
            if low_importance_votes[feature_idx] >= min_votes_needed:
                features_to_remove.append(feature_idx)
        
        print(f"Initial features identified for removal by consensus: {len(features_to_remove)}")

        if not features_to_remove:
            print("No consensus reached for feature removal. Falling back to average ranking approach...")
            avg_ranks = {
                feature_idx: np.mean(feature_rankings[feature_idx])
                if feature_idx in feature_rankings else current_num_features  # Assign worst rank if no data
                for feature_idx in range(current_num_features)
            }
            num_to_remove = max(1, int(current_num_features * 0.1))  # Remove at least 1, or 10%
            print(f"Removing {num_to_remove} features based on lowest average rank.")
            sorted_by_avg_rank = sorted(avg_ranks.items(), key=lambda x: x[1])
            features_to_remove = [idx for idx, _ in sorted_by_avg_rank[:num_to_remove]]
            print(f"Features selected for removal by average rank: {len(features_to_remove)}")

        # Ensure exactly target_threshold at the end
        remaining_after_removal = current_num_features - len(features_to_remove)
        if remaining_after_removal < target_threshold:
            excess_removal = target_threshold - remaining_after_removal
            if excess_removal > 0:
                print(f"Adjusting removal: Would remove too many features. Keeping {excess_removal} features.")
                features_to_remove = features_to_remove[:-excess_removal]
            
        if not features_to_remove:
            print("No features to remove in this iteration. Stopping feature elimination.")
            break

        drop_features_original_names = cur_features.columns[features_to_remove]
        print(f"Removing {len(drop_features_original_names)} features from the dataset.")
        cur_features = cur_features.drop(columns=drop_features_original_names)
        print(f"Features remaining after iteration {iteration}: {cur_features.shape[1]}")

        # Log dropped features
        dropped_features_filename = f"dropped_features_soap_rfe_iter_{iteration}.txt"
        pd.Series(drop_features_original_names).to_csv(dropped_features_filename, index=False)
        print(f"Logged dropped features to {dropped_features_filename}")

        # Also append to timestamped concatenated log
        with open(dropped_concat_filename, 'a') as f:
            f.write(f"# iter={iteration} time={time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            for feat in drop_features_original_names:
                f.write(f"{feat}\n")
            f.write("\n")
        print(f"Appended dropped features to {dropped_concat_filename}")

        print(f"Iteration {iteration} completed in {time.time() - iter_start_time:.2f} seconds.")

    print(f"\nFinished robust feature elimination. Final feature count: {cur_features.shape[1]}")
    return cur_features


def main():
    parser = argparse.ArgumentParser(description="Advanced RFE for SOAP features")
    parser.add_argument("--input", required=True, help="Input CSV file with SOAP features")
    parser.add_argument("--target_col", default="e_form", help="Target column name")
    parser.add_argument("--n_features", type=int, default=300, help="Number of features to select")
    parser.add_argument("--output", help="Output CSV file (default: input_rfe.csv)")
    parser.add_argument("--correlation_threshold", type=float, default=0.98, 
                       help="Correlation threshold for initial filtering")
    parser.add_argument("--n_jobs", type=int, default=24, help="Number of parallel jobs")
    parser.add_argument("--n_folds", type=int, default=5, help="Number of CV folds")
    parser.add_argument("--consensus_threshold", type=float, default=0.6, 
                       help="Consensus threshold for feature removal")
    
    args = parser.parse_args()
    
    # Set default output filename
    if args.output is None:
        args.output = args.input.replace(".csv", "_advanced_rfe.csv")
    
    print(f"Loading data from: {args.input}")
    df = pd.read_csv(args.input)
    print(f"Loaded dataset with shape: {df.shape}")
    
    # Separate features and target
    if args.target_col not in df.columns:
        raise ValueError(f"Target column '{args.target_col}' not found in dataset")
    
    feature_cols = [c for c in df.columns if c.startswith("SOAP")]
    other_cols = [c for c in df.columns if not c.startswith("SOAP")]
    
    print(f"Found {len(feature_cols)} SOAP features")
    print(f"Other columns: {other_cols}")
    
    features_df = df[feature_cols].copy()
    targets = df[args.target_col].copy()
    
    # Remove highly correlated features first
    features_df = remove_correlated_features(features_df, args.correlation_threshold)
    
    # Apply robust RFE
    selected_features_df = xgb_robust_preselection(
        features_df, targets, 
        target_threshold=args.n_features,
        n_jobs=args.n_jobs,
        n_folds=args.n_folds,
        consensus_threshold=args.consensus_threshold
    )
    
    # Combine with other columns
    result_df = pd.concat([df[other_cols], selected_features_df], axis=1)
    
    # Save result
    result_df.to_csv(args.output, index=False)
    print(f"Saved RFE result to: {args.output}")
    
    # Save selected feature names
    feature_list_path = args.output.replace(".csv", "_selected_features.txt")
    with open(feature_list_path, "w") as f:
        for feat in selected_features_df.columns:
            f.write(f"{feat}\n")
    print(f"Saved selected feature list to: {feature_list_path}")
    
    print(f"\nFinal dataset shape: {result_df.shape}")
    print(f"Selected {len(selected_features_df.columns)} SOAP features out of {len(feature_cols)} original features")


if __name__ == "__main__":
    main()
