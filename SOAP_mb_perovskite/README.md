# SOAP Featurization for Matbench Perovskites

This directory contains scripts for performing SOAP (Smooth Overlap of Atomic Positions) featurization on the matbench_perovskites dataset using parallel processing and feature selection.

## Overview

The workflow consists of several components:

1. **Dataset Preparation**: Split the matbench_perovskites dataset into chunks for parallel processing
2. **SOAP Featurization**: Apply SOAP descriptors using the master configuration with mu1nu1 compression
3. **Parallel Processing**: Use SLURM array jobs to process chunks in parallel
4. **Result Combination**: Combine processed chunks into a single dataset
5. **Feature Selection**: Apply Recursive Feature Elimination (RFE) to reduce to 300 features

## Master SOAP Configuration

The featurization uses the following master SOAP parameters:
- `r_cut`: 5.0 Å (cutoff radius)
- `n_max`: 6 (maximum radial basis functions)
- `l_max`: 6 (maximum angular momentum)
- `sigma`: 0.5 (Gaussian width)
- `compression`: mu1nu1 mode
- `periodic`: True (for crystal structures)
- `species`: All chemical elements (excluding noble gases)

## Files

### Core Scripts
- `get_matbench_perovskites.py`: Dataset preparation and chunking
- `soap_featurize_perovskites.py`: Main SOAP featurization script
- `run_soap_workflow.sh`: Master workflow coordinator

### SLURM Submission Scripts
- `submit_prepare.sh`: Submit data preparation job
- `submit.sh`: Submit SOAP featurization array job
- `submit_combine.sh`: Submit chunk combination and RFE job

### Reference Files
- `soap_featurization_example.py`: Original SOAP configuration example
- `context_code_for_rfe.py`: RFE implementation reference
- `env_to_use.txt`: Conda environment specification

## Usage

### Quick Start (Automated Workflow)

```bash
# Make scripts executable
chmod +x *.sh

# Run the complete workflow
./run_soap_workflow.sh
```

This will automatically:
1. Prepare the dataset and create chunks
2. Submit parallel SOAP featurization jobs
3. Combine results and perform RFE
4. Generate final feature files

### Manual Step-by-Step Execution

#### Step 1: Prepare Dataset
```bash
# Submit data preparation job
sbatch submit_prepare.sh

# Or run directly
python3 get_matbench_perovskites.py --chunk_size 100 --output_dir chunks --save_full
```

#### Step 2: SOAP Featurization
```bash
# Update array range based on number of chunks
N_CHUNKS=$(tail -n +2 chunks/chunk_info.csv | wc -l)
sed -i "s/#SBATCH --array=.*/#SBATCH --array=0-$((N_CHUNKS-1))%10/" submit.sh

# Submit array job
sbatch submit.sh

# Or process single chunk for testing
python3 soap_featurize_perovskites.py --chunk_id 0 --chunks_dir chunks --output_dir features
```

#### Step 3: Combine and Apply RFE
```bash
# Submit combination job
sbatch submit_combine.sh

# Or run directly
python3 soap_featurize_perovskites.py --combine_chunks features --rfe --rfe_features 300
```

## Output Files

### Intermediate Files
- `chunks/`: Directory containing data chunks
  - `chunk_XXXX.pkl`: Individual data chunks
  - `chunk_info.csv`: Chunk metadata
- `features/`: Directory containing processed features
  - `chunk_XXXX_features.csv`: SOAP features for each chunk
- `logs/`: SLURM job logs

### Final Results
- `matbench_perovskites_prepared.pkl`: Full prepared dataset
- `matbench_perovskites_soap_features.csv`: Combined SOAP features
- `matbench_perovskites_soap_rfe.csv`: RFE-reduced features (300 features)
- `matbench_perovskites_soap_rfe_selected_features.txt`: List of selected features

## Command Line Options

### get_matbench_perovskites.py
```bash
python3 get_matbench_perovskites.py [options]
  --chunk_size INT     Number of samples per chunk (default: 100)
  --output_dir STR     Output directory for chunks (default: chunks)
  --save_full          Also save the full dataset as a single file
```

### soap_featurize_perovskites.py
```bash
python3 soap_featurize_perovskites.py [options]
  --chunk_id INT       Process specific chunk ID (for array jobs)
  --chunks_dir STR     Directory containing data chunks (default: chunks)
  --output_dir STR     Output directory for features (default: features)
  --combine_chunks STR Combine chunks from specified directory
  --rfe                Perform RFE feature selection
  --rfe_features INT   Number of features to select with RFE (default: 300)
```

## Resource Requirements

### Data Preparation
- Time: ~30 minutes
- Memory: ~8 GB per CPU
- CPUs: 4

### SOAP Featurization (per chunk of 100 samples)
- Time: ~2-4 hours
- Memory: ~24 GB per CPU
- CPUs: 24

### Combination and RFE
- Time: ~1-2 hours
- Memory: ~16 GB per CPU
- CPUs: 8

## Troubleshooting

### Common Issues

1. **Memory errors during featurization**
   - Reduce chunk size in data preparation
   - Increase memory allocation in SLURM scripts

2. **Failed array jobs**
   - Check log files in `logs/` directory
   - Resubmit specific failed chunks manually

3. **Missing dependencies**
   - Ensure conda environment is properly activated
   - Check that all required packages are installed

### Monitoring Progress

```bash
# Check job status
squeue -u $USER

# Monitor chunk completion
ls features/*_features.csv | wc -l

# Check log files
tail -f logs/soap_chunk_*.out
```

## Expected Results

For the matbench_perovskites dataset (~18,000 samples):
- Initial SOAP features: ~50,000-100,000 features
- After RFE: 300 features
- Processing time: ~8-12 hours total (with parallel processing)
- Final dataset size: ~18,000 samples × 300 features

## Environment

The scripts use the conda environment specified in `env_to_use.txt`:
```bash
conda activate /globalscratch/users/r/g/rgouvea/test_env_modnet_pgnn/modnetpgnn_env
```

Required packages:
- pymatgen
- matminer
- dscribe
- scikit-learn
- pandas
- numpy
- pickle
