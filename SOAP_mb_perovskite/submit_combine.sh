#!/bin/bash
#SBATCH --job-name=SOAP_combine_chunks
#SBATCH --time=0-02:00:00
#SBATCH --output=logs/combine_chunks.out
#SBATCH --error=logs/combine_chunks.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=8
#SBATCH --partition=keira
#SBATCH --mem-per-cpu=16000

# Create logs directory if it doesn't exist
mkdir -p logs

source ~/.bashrc
conda activate /globalscratch/users/r/g/rgouvea/test_env_modnet_pgnn/modnetpgnn_env

echo "Starting chunk combination and RFE"
echo "Job ID: ${SLURM_JOB_ID}"
echo "Node: $(hostname)"
date

# Combine chunks and perform RFE
python3 soap_featurize_perovskites.py \
    --combine_chunks features \
    --rfe \
    --rfe_features 300

echo "Chunk combination and RFE completed"
date
