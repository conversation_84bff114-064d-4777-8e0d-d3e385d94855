#!/bin/bash
#SBATCH --job-name=SOAP_perovskites_array
#SBATCH --time=0-01:00:00
#SBATCH --output=logs/soap_chunk_%a.out
#SBATCH --error=logs/soap_chunk_%a.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
# SBATCH --partition=keira
#SBATCH --mem-per-cpu=10000
#SBATCH --array=182-189

# Create logs directory if it doesn't exist
mkdir -p logs

source ~/.bashrc
conda activate /globalscratch/users/r/g/rgouvea/test_env_modnet_pgnn/modnetpgnn_env

echo "Starting SOAP featurization for chunk ${SLURM_ARRAY_TASK_ID}"
echo "Job ID: ${SLURM_JOB_ID}"
echo "Array Task ID: ${SLURM_ARRAY_TASK_ID}"
echo "Node: $(hostname)"
date

# Run SOAP featurization for this specific chunk
python3 soap_featurize_perovskites.py \
    --chunk_id ${SLURM_ARRAY_TASK_ID} \
    --chunks_dir chunks \
    --output_dir features

echo "Completed chunk ${SLURM_ARRAY_TASK_ID}"
date
