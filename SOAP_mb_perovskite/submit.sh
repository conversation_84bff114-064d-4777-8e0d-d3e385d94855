#!/bin/bash
#SBATCH --job-name=MODNet_perovskites_OMEGAnn1_000
#SBATCH --time=0-05:00:00
#SBATCH --output=log.txt
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=24
#SBATCH --partition=keira
#SBATCH --mem-per-cpu=24000
source ~/.bashrc

conda activate /globalscratch/users/r/g/rgouvea/test_env_modnet_pgnn/modnetpgnn_env
echo "start"
date
nproc=24 # $(nproc --all)
python3 python_script.py --n_jobs $nproc >> log.txt
echo "done"
date
