#!/usr/bin/env python
import argparse
import glob
import os
import re
import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import KFold
from collections import defaultdict
from sklearn.preprocessing import MinMaxScaler
from joblib import Parallel, delayed
import time # Import the time module
from modnet.preprocessing import MODData
import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import KFold
from collections import defaultdict
from sklearn.preprocessing import MinMaxScaler

# Decorator for timing functions
def time_function(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        print(f"--- Starting '{func.__name__}' ---")
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"--- Finished '{func.__name__}' in {end_time - start_time:.2f} seconds ---")
        return result
    return wrapper

@time_function
def remove_correlated_features(df, threshold=0.98):
    """
    Remove features that are highly correlated (absolute correlation > threshold).
    Keeps the first feature in the pair and drops the rest.
    """
    print(f"Running correlation filter with threshold={threshold} ...")
    corr_matrix = df.corr().abs()
    upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
    to_drop = [column for column in upper.columns if any(upper[column] > threshold)]
    if to_drop:
        print(f" - Dropping {len(to_drop)} highly correlated features before preselection.")
        df = df.drop(columns=to_drop)
    else:
        print(" - No highly correlated features found.")
    return df


def rename_cols(df):
    """
    Make column names XGBoost-compatible by replacing problematic characters.
    """
    df.columns = [
        col.replace("[", "_").replace("]", "_").replace(" ", "_")
           .replace("<", "_").replace(">", "_").replace(",", "_")
           .replace("|", "_").replace(".", "_")
        for col in df.columns
    ]
    return df

@time_function
def xgb_robust_preselection(data, feature_sets_str, n_jobs=24, target_threshold=800,  
                            n_folds=5, consensus_threshold=0.6,  
                            importance_percentile=10):
    """
    Performs robust feature elimination using XGBoost importance ranks across CV folds.
    Stops at exactly target_threshold features.
    """
    cur_features = data.df_featurized.copy()
    initial_num = cur_features.shape[1]

    if initial_num <= target_threshold:
        print(f"Feature count ({initial_num}) already at/below target. Skipping selection.")
        return data

    print(f"Starting robust feature elimination from {cur_features.shape[1]} features...")
    print(f"Using {n_folds}-fold CV with {consensus_threshold*100:.0f}% consensus threshold")
    print(f"Features in bottom {importance_percentile}th percentile of ranks are candidates for removal.")

    # Prepare target
    y_numeric = pd.to_numeric(data.df_targets.values.ravel(), errors='coerce')
    if np.isnan(y_numeric).any():
        raise ValueError("Error: Found NaNs in target variable.")
    print(f"Target variable prepared. Shape: {y_numeric.shape}")

    # --- Step 2: Prepare scaler ONCE ---
    print("Preparing scaler and scaling features...")
    xgb_compatible_features = rename_cols(cur_features.copy())
    X_numeric_temp = xgb_compatible_features.apply(pd.to_numeric, errors='coerce')
    scaler = MinMaxScaler()
    X_scaled = pd.DataFrame(
        scaler.fit_transform(X_numeric_temp),
        columns=X_numeric_temp.columns,
        index=X_numeric_temp.index
    )
    X_numeric_full = X_scaled.fillna(-1)
    print(f"Features scaled and NaNs imputed. Full scaled data shape: {X_numeric_full.shape}")

    # Prepare a timestamped aggregator file for this run
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    dropped_concat_filename = f"dropped_features_{feature_sets_str}_{timestamp}.txt"
    print(f"Concatenating dropped features for this run into: {dropped_concat_filename}")

    iteration = 0
    while cur_features.shape[1] > target_threshold:
        iteration += 1
        current_num_features = cur_features.shape[1]
        print(f"\nIteration {iteration}: {current_num_features} features remaining.")
        iter_start_time = time.time()

        # Subset scaled data to current features
        X_numeric = X_numeric_full[cur_features.columns]
        print(f"Subsetting data for current iteration. Shape: {X_numeric.shape}")

        feature_rankings = defaultdict(list)
        low_importance_votes = defaultdict(int)

        kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)
        print(f"Starting {n_folds}-fold cross-validation...")

        for fold_idx, (train_idx, val_idx) in enumerate(kf.split(X_numeric)):
            fold_start_time = time.time()
            X_train, y_train = X_numeric.iloc[train_idx], y_numeric[train_idx]
            print(f"  Fold {fold_idx + 1}/{n_folds}: Training data shape X={X_train.shape}, y={y_train.shape}")

            xgb_model = xgb.XGBRegressor(
                n_jobs=n_jobs,
                random_state=42,
                objective='reg:squarederror',
                n_estimators=100,
                subsample=0.9, # new
                colsample_bytree=0.9, # new
                min_child_weight=2, # new
                max_depth=6
            )

            try:
                model_fit_start = time.time()
                xgb_model.fit(X_train.values, y_train)
                model_fit_end = time.time()
                print(f"  Fold {fold_idx + 1}: XGBoost model fitted in {model_fit_end - model_fit_start:.2f} seconds.")
                
                importances = xgb_model.feature_importances_
                current_ranks = np.argsort(np.argsort(importances)) # 0 = least important, N-1 = most important

                for feature_idx, rank in enumerate(current_ranks):
                    feature_rankings[feature_idx].append(rank)

                rank_threshold = np.percentile(current_ranks, importance_percentile)
                print(f"  Fold {fold_idx + 1}: Rank threshold for removal candidate: {rank_threshold:.2f} (bottom {importance_percentile}th percentile)")
                
                for feature_idx, rank in enumerate(current_ranks):
                    if rank <= rank_threshold:
                        low_importance_votes[feature_idx] += 1
                print(f"  Fold {fold_idx + 1} completed in {time.time() - fold_start_time:.2f} seconds.")

            except Exception as e:
                print(f"  Error in fold {fold_idx + 1}: {e}")
                continue

        # --- Consensus removal ---
        print("\nEvaluating consensus for feature removal...")
        features_to_remove = []
        min_votes_needed = int(n_folds * consensus_threshold)
        print(f"Minimum votes needed for consensus: {min_votes_needed} out of {n_folds} folds.")

        for feature_idx in range(current_num_features):
            if low_importance_votes[feature_idx] >= min_votes_needed:
                features_to_remove.append(feature_idx)
        
        print(f"Initial features identified for removal by consensus: {len(features_to_remove)}")

        if not features_to_remove:
            print("No consensus reached for feature removal. Falling back to average ranking approach...")
            avg_ranks = {
                feature_idx: np.mean(feature_rankings[feature_idx])
                if feature_idx in feature_rankings else current_num_features # Assign worst rank if no data
                for feature_idx in range(current_num_features)
            }
            num_to_remove = max(1, int(current_num_features * 0.1)) # Remove at least 1, or 10%
            print(f"Removing {num_to_remove} features based on lowest average rank.")
            sorted_by_avg_rank = sorted(avg_ranks.items(), key=lambda x: x[1])
            features_to_remove = [idx for idx, _ in sorted_by_avg_rank[:num_to_remove]]
            print(f"Features selected for removal by average rank: {len(features_to_remove)}")

        # Ensure exactly target_threshold at the end
        remaining_after_removal = current_num_features - len(features_to_remove)
        if remaining_after_removal < target_threshold:
            excess_removal = target_threshold - remaining_after_removal
            if excess_removal > 0:
                print(f"Adjusting removal: Would remove too many features. Keeping {excess_removal} features.")
                features_to_remove = features_to_remove[:-excess_removal]
            
        if not features_to_remove:
            print("No features to remove in this iteration. Stopping feature elimination.")
            break

        drop_features_original_names = cur_features.columns[features_to_remove]
        print(f"Removing {len(drop_features_original_names)} features from the dataset.")
        cur_features = cur_features.drop(columns=drop_features_original_names)
        print(f"Features remaining after iteration {iteration}: {cur_features.shape[1]}")

        # Log dropped features per-iteration (original behavior)
        dropped_features_filename = f"dropped_features_{feature_sets_str}_iter_{iteration}.txt"
        pd.Series(drop_features_original_names).to_csv(dropped_features_filename, index=False)
        print(f"Logged dropped features to {dropped_features_filename}")

        # Also append to a timestamped concatenated log for this run
        with open(dropped_concat_filename, 'a') as f:
            # Write a header for readability per iteration
            f.write(f"# iter={iteration} time={time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            for feat in drop_features_original_names:
                f.write(f"{feat}\n")
            # blank line between iterations
            f.write("\n")
        print(f"Appended dropped features to {dropped_concat_filename}")

        print(f"Iteration {iteration} completed in {time.time() - iter_start_time:.2f} seconds.")

    data.df_featurized = cur_features
    print(f"\nFinished robust feature elimination. Final feature count: {cur_features.shape[1]}")
    return data

@time_function
def xgb_stability_preselection(data, n_jobs=24, target_threshold=800, 
                              n_bootstrap=10, stability_threshold=0.7):
    """
    Feature selection based on stability of importance rankings across bootstrap samples.
    Features are Min-Max scaled, and NaNs are imputed with -1.
    """
    def rename_cols(df):
        df.columns = [col.replace("[", "_").replace("]", "_").replace(" ", "_") \
                          .replace("<","_").replace(">","_").replace(",","_") \
                          .replace("|","_").replace(".","_")
                          for col in df.columns]
        return df

    cur_features = data.df_featurized.copy()
    initial_num = cur_features.shape[1]

    if initial_num <= target_threshold:
        print(f"Feature count ({initial_num}) already at/below target. Skipping stability selection.")
        return data

    print(f"Starting stability-based selection from {initial_num} features...")
    print(f"Using {n_bootstrap} bootstrap samples with stability threshold {stability_threshold}.")

    # Prepare data
    print("Preparing data for stability selection...")
    xgb_compatible_features = rename_cols(cur_features.copy())

    # Apply Min-Max Scaling and then fill NaNs with -1
    X_numeric_temp = xgb_compatible_features.apply(pd.to_numeric, errors='coerce')
    scaler = MinMaxScaler()
    X_scaled = pd.DataFrame(scaler.fit_transform(X_numeric_temp), 
                            columns=X_numeric_temp.columns, 
                            index=X_numeric_temp.index)
    X_numeric = X_scaled.fillna(-1)
    print(f"Features scaled and NaNs imputed. Shape: {X_numeric.shape}")

    y_numeric = pd.to_numeric(data.df_targets.values.ravel(), errors='coerce')
    print(f"Target variable prepared. Shape: {y_numeric.shape}")

    # Bootstrap sampling and importance calculation
    feature_importance_matrix = []
    n_samples = len(X_numeric)
    print(f"Starting {n_bootstrap} bootstrap runs...")

    for bootstrap_idx in range(n_bootstrap):
        bootstrap_start_time = time.time()
        sample_indices = np.random.choice(n_samples, size=n_samples, replace=True)
        X_boot = X_numeric.iloc[sample_indices]
        y_boot = y_numeric[sample_indices]
        print(f"  Bootstrap {bootstrap_idx + 1}/{n_bootstrap}: Sampled {len(sample_indices)} data points.")

        xgb_model = xgb.XGBRegressor(
            n_jobs=n_jobs, 
            random_state=bootstrap_idx, 
            objective='reg:squarederror',
            n_estimators=100
        )

        try:
            model_fit_start = time.time()
            xgb_model.fit(X_boot.values, y_boot)
            model_fit_end = time.time()
            print(f"  Bootstrap {bootstrap_idx + 1}: XGBoost model fitted in {model_fit_end - model_fit_start:.2f} seconds.")
            importances = xgb_model.feature_importances_
            feature_importance_matrix.append(importances)
            print(f"  Bootstrap {bootstrap_idx + 1} completed in {time.time() - bootstrap_start_time:.2f} seconds.")
        except Exception as e:
            print(f"  Error in bootstrap {bootstrap_idx + 1}: {e}")
            continue

    if not feature_importance_matrix:
        print("No successful bootstrap runs. Falling back to original method (no selection applied).")
        return data

    importance_matrix = np.array(feature_importance_matrix)
    print(f"Collected importance matrix of shape: {importance_matrix.shape}")

    print("Calculating stability scores...")
    mean_importance = np.mean(importance_matrix, axis=0)
    std_importance = np.std(importance_matrix, axis=0)
    cv_scores = std_importance / (mean_importance + 1e-8) # Coefficient of Variation
    
    rank_correlations = []
    for i in range(len(feature_importance_matrix)):
        for j in range(i+1, len(feature_importance_matrix)):
            # Ensure there are enough features for correlation calculation
            if len(feature_importance_matrix[i]) > 1 and len(feature_importance_matrix[j]) > 1:
                rank_corr = np.corrcoef(
                    np.argsort(feature_importance_matrix[i]),
                    np.argsort(feature_importance_matrix[j])
                )[0, 1]
                if not np.isnan(rank_corr):
                    rank_correlations.append(rank_corr)

    avg_rank_correlation = np.mean(rank_correlations) if rank_correlations else 0
    print(f"Average rank correlation across bootstraps: {avg_rank_correlation:.3f}")

    stability_scores = 1 / (1 + cv_scores) # Higher is more stable
    combined_scores = mean_importance * stability_scores # Prioritize important and stable features

    top_feature_indices = np.argsort(combined_scores)[-target_threshold:]
    selected_features = cur_features.columns[top_feature_indices]

    data.df_featurized = cur_features[selected_features]
    print(f"Selected {len(selected_features)} most stable and important features. Final feature count: {data.df_featurized.shape[1]}")

    return data

# -------------------------- main() function --------------------------  
def main(matbench_set, feature_sets):  
    overall_start_time = time.time()
    print(f"--- Starting main process for matbench_set: {matbench_set}, feature_sets: {feature_sets} ---")

    # ---------------------------------------------------------------------------  
    # File selection using glob and pattern matching  
    # ---------------------------------------------------------------------------  
    file_selection_start_time = time.time()
    data_path_pattern = ''    
    if any("cogn" in fs for fs in feature_sets) and not any("sissolvl2" in fs for fs in feature_sets) and not any("sissolvl3" in fs for fs in feature_sets):    
        data_path_pattern = f'../data/{matbench_set}/{matbench_set}_featurizedMM2020*_mattervial_coGNadj.csv'    
        print(f"Detected 'coGN' in feature_sets. Using specific data file: {data_path_pattern}")    
    elif any("cogn" in fs for fs in feature_sets) and any("sissolvl2" in fs for fs in feature_sets) and not any("sissolvl3" in fs for fs in feature_sets):   
        data_path_pattern = f'matbench_perovskites/matbench_perovskites_featurizedMM2020Struct_mattervial_coGNadj_SISSOaugmented2.csv'     
        print(f"Detected 'coGN' and 'sissolvl2' in feature_sets. Using specific data file: {data_path_pattern}")
    elif any("cogn" in fs for fs in feature_sets) and any("sissolvl3" in fs for fs in feature_sets):   
        data_path_pattern = f'matbench_perovskites/matbench_perovskites_featurizedMM2020Struct_mattervial_coGNadj_SISSOaugmented3.csv'     
        print(f"Detected 'coGN' and 'sissolvl3' in feature_sets. Using specific data file: {data_path_pattern}")
    else:    
        data_path_pattern = f'../data/{matbench_set}/{matbench_set}_featurizedMM2020*_mattervial.csv'    
        print(f"No 'coGN' detected in feature_sets. Using default data file pattern: {data_path_pattern}")  
    
    matching_files = glob.glob(data_path_pattern)  
    if not matching_files:  
        raise FileNotFoundError(f"No files found for pattern: {data_path_pattern}")  
    elif len(matching_files) > 1:  
        print(f"Multiple files found, selecting the first one:\n  {matching_files[0]}")  
    data_path = matching_files[0]  
    print(f"Using data file: {data_path}")  
    print(f"File selection completed in {time.time() - file_selection_start_time:.2f} seconds.")

    # ---------------------------------------------------------------------------  
    # Load CSV and check essential columns  
    # ---------------------------------------------------------------------------  
    load_csv_start_time = time.time()
    df_all = pd.read_csv(data_path)  
    print(f"Loaded data with shape: {df_all.shape}")  
    required_cols = ['material_id', 'target']  
    has_composition = 'composition' in df_all.columns  
    has_structure = 'structure' in df_all.columns  
    if not (has_composition or has_structure):  
        raise ValueError("Input CSV must contain either a 'composition' or 'structure' column.")  
    # Prefer composition if available  
    input_type = 'composition' if has_composition else 'structure'  
    print(f"Determined input type: {input_type}")
    for col in required_cols:  
        if col not in df_all.columns:  
            raise ValueError(f"Input CSV must contain column: {col}")  
    print(f"CSV loading and initial checks completed in {time.time() - load_csv_start_time:.2f} seconds.")

    # ---------------------------------------------------------------------------  
    # Define feature group patterns  
    # ---------------------------------------------------------------------------  
    # (No timing needed for static dictionary definition)
    MATMINER_PREFIXES = [  
        'AtomicOrbitals|', 'AtomicPackingEfficiency|', 'BandCenter|', 'ElementFraction|',  
        'ElementProperty|', 'IonProperty|', 'Miedema|', 'Stoichiometry|',  
        'TMetalFraction|', 'ValenceOrbital|', 'YangSolidSolution|',  
        'ElectronegativityDiff|', 'OxidationStates|', 'DensityFeatures|',  
        'GlobalSymmetryFeatures|', 'CoulombMatrix|', 'SineCoulombMatrix|',  
        'BondFractions|', 'StructuralHeterogeneity|', 'MaximumPackingEfficiency|',  
        'ChemicalOrdering|', 'XRDPowderPattern|', 'RadialDistributionFunction|',  
        'AGNIFingerPrint|', 'AverageBondAngle|', 'AverageBondLength|',  
        'BondOrientationParameter|', 'ChemEnvSiteFingerprint|', 'CoordinationNumber|',  
        'CrystalNNFingerprint|', 'GaussianSymmFunc|', 'GeneralizedRDF|',  
        'LocalPropertyDifference|', 'OPSiteFingerprint|', 'VoronoiFingerprint|',
        'AtomicOrbitals_', 'AtomicPackingEfficiency_', 'BandCenter_', 'ElementFraction_',
            'ElementProperty_', 'IonProperty_', 'Miedema_', 'Stoichiometry_',
            'TMetalFraction_', 'ValenceOrbital_', 'YangSolidSolution_',
            'ElectronegativityDiff_', 'OxidationStates_', 'DensityFeatures_',
            'GlobalSymmetryFeatures_', 'CoulombMatrix_', 'SineCoulombMatrix_',
            'BondFractions_', 'StructuralHeterogeneity_', 'MaximumPackingEfficiency_',
            'ChemicalOrdering_', 'XRDPowderPattern_', 'RadialDistributionFunction_',
            'AGNIFingerPrint_', 'AverageBondAngle_', 'AverageBondLength_',
            'BondOrientationParameter|', 'ChemEnvSiteFingerprint_', 'CoordinationNumber_',
            'CrystalNNFingerprint_', 'GaussianSymmFunc_', 'GeneralizedRDF_',
            'LocalPropertyDifference_', 'OPSiteFingerprint_', 'VoronoiFingerprint_'  
    ]  
    MATMINER_PATTERN = r'^(' + '|'.join([re.escape(p) for p in MATMINER_PREFIXES]) + ')'  
    
    FEATURE_GROUP_PATTERNS = {
        'matminer': MATMINER_PATTERN,
        'sisso': r'^SISSO_',
        'sisso_v2': r'^SISSOv2_',
        'sissolvl2': r'^SISSOlvl2_',
        'sisso_mb_dielectric':  r'^SISSO_matbench_dielectric_',
        'sisso_mb_phonons':     r'^SISSO_matbench_phonons_',
        'sisso_mb_perovskites': r'^SISSO_matbench_perovskites_',
        'sisso_mb_expt_is_metal': r'^SISSO_matbench_expt_is_metal_',
        'sisso_mb_steels':      r'^SISSO_matbench_steels_',
        'sisso_mb_jdft2d':      r'^SISSO_matbench_jdft2d_',
        'sisso_mb_log_gvrh':    r'^SISSO_matbench_log_gvrh_',
        'sisso_mb_mp_e_form':   r'^SISSO_matbench_mp_e_form_',
        'sisso_noemd_hse_pbe_diff':   r'^SISSO_noemd_hse_pbe_diff_',
        'sisso_noemd_shg':            r'^SISSO_noemd_shg_',
        'sisso_mb_log_kvrh':    r'^SISSO_matbench_log_kvrh_',
        'sisso_mb_glass':       r'^SISSO_matbench_glass_',
        'sisso_mb_mp_is_metal': r'^SISSO_matbench_mp_is_metal_',
        'sisso_mb_mp_gap':      r'^SISSO_matbench_mp_gap_',
        'sisso_mb_expt_gap':    r'^SISSO_matbench_expt_gap_',
        'roost_mpgap_lo': r'^ROOST_mpgap_LayerOutput_',
        'roost_mpgap_lmp': r'^ROOST_mpgap_LayerMaterialPooling_',
        'roost_oqmd_lo': r'^ROOST_oqmd_eform_LayerOutput_',
        'roost_oqmd_lmp': r'^ROOST_oqmd_eform_LayerMaterialPooling_',
        'megnet_mm': r'^MEGNet_MatMinerEncoded_v1_',
        'megnet_ofm': r'^MEGNet_OFMEncoded_v1_',
        'mvl32': r'^MVL32_',
        'mvl16': r'^MVL16_',
        # Aliases for convenience
        'roost_lmp': r'^ROOST_.*_LayerMaterialPooling_',
        'roost_lo': r'^ROOST_.*_LayerOutput_',
        'roost_mpgap': r'^ROOST_mpgap_',
        'roost_oqmd': r'^ROOST_oqmd_eform_',
        'roost_all': r'^ROOST_',
        'megnet_all': r'^MEGNet_',
        'mvl_all': r'^MVL(16|32)_',
        'orb_v3': r'^ORB_v3_',
        'cogn_fold0': r'^coGN_.*_fold0',
        'cogn_fold1': r'^coGN_.*_fold1',
        'cogn_fold2': r'^coGN_.*_fold2',
        'cogn_fold3': r'^coGN_.*_fold3',
        'cogn_fold4': r'^coGN_.*_fold4',
        'ofm': r'^OFM:', # for reconstructed OFM features
        'sisso_residuals': r'^SISSOresiduals_',
        'sissolvl3': r'SISSOlvl3_'
    }
    feature_sets_str = "_".join(feature_sets) 
    # ---------------------------------------------------------------------------  
    # Feature Selection  
    # ---------------------------------------------------------------------------  
    feature_selection_start_time = time.time()
    print(f"\nSelecting features for feature_sets: {feature_sets}")  
    core_cols_to_drop = ['material_id', input_type, 'target']  
    all_feature_cols = df_all.drop(columns=core_cols_to_drop, errors='ignore').columns  
    print(f"Total columns in raw data (excluding core): {len(all_feature_cols)}")
    
    selected_feature_cols = set()  
    for set_name in feature_sets:  
        if set_name in FEATURE_GROUP_PATTERNS:  
            pattern = FEATURE_GROUP_PATTERNS[set_name]  
            print(f" - Matching columns for '{set_name}' using pattern: {pattern}")  
            matches = [col for col in all_feature_cols if re.match(pattern, col)]  
            if not matches:  
                print(f"   Warning: No columns found matching the pattern for '{set_name}'.")  
            else:  
                print(f"   Found {len(matches)} columns for '{set_name}'.")  
                selected_feature_cols.update(matches)  
        else:  
            matches = [col for col in all_feature_cols if col.startswith(set_name)]  
            if matches:  
                print(f" - Matching columns for '{set_name}' using prefix matching.")  
                print(f"   Found {len(matches)} columns for '{set_name}'.")  
                selected_feature_cols.update(matches)  
            else:  
                raise ValueError(  
                    f"Unknown or unmatched feature set name: '{set_name}'. "  
                    f"Valid keys: {list(FEATURE_GROUP_PATTERNS.keys())}"  
                )  
    
    selected_feature_cols = sorted(list(selected_feature_cols))  
    if not selected_feature_cols:  
        raise ValueError("No features selected. Check your feature_sets and CSV column names/patterns.")  
   
    # ---------------------------------------------------------------------------
    # Resume from previously dropped features (optional but recommended)
    # ---------------------------------------------------------------------------
    resume_from_prior = True  # set to False to disable
    if resume_from_prior:
        # Collect both per-iteration logs and timestamped concatenated logs
        patterns = [
            f"dropped_features_*{feature_sets_str}*_iter_*.txt",   # per-iteration logs
            f"dropped_features_{feature_sets_str}_*.txt",          # concatenated logs with timestamp
        ]
        dropped_files = []
        for pat in patterns:
            dropped_files.extend(glob.glob(pat))
        # Deduplicate and sort (so behavior is stable)
        dropped_files = sorted(set(dropped_files))

        if dropped_files:
            print(f"\nResuming: found {len(dropped_files)} dropped-feature logs for this feature set:")
            for f in dropped_files:
                print(f"  - {f}")

            dropped_union = set()

            def _load_dropped_list(path):
                # Try to robustly parse either 1-col CSV or text file with headers and blanks
                feats = []
                try:
                    # First attempt: simple 1-column CSV (your per-iteration files)
                    df_d = pd.read_csv(path, header=None)
                    col = df_d.columns[0]
                    feats = [str(x) for x in df_d[col].tolist()]
                except Exception:
                    # Fallback: line-by-line parse (useful for concatenated timestamped file)
                    with open(path, "r") as fh:
                        for line in fh:
                            s = line.strip()
                            if not s:
                                continue
                            if s.startswith("#"):
                                continue  # skip iteration/time headers
                            feats.append(s)
                return feats

            for f in dropped_files:
                try:
                    feats = _load_dropped_list(f)
                    # Filter out obvious noise (e.g., any accidental column names like '0')
                    feats = [x for x in feats if x not in ("0", "Unnamed: 0")]
                    dropped_union.update(feats)
                except Exception as e:
                    print(f"  Warning: failed to parse {f}: {e}")

            if dropped_union:
                before = len(selected_feature_cols)
                selected_feature_cols = [c for c in selected_feature_cols if c not in dropped_union]
                after = len(selected_feature_cols)
                print(f"Resuming: removed {before - after} previously dropped features. Now {after} remain.")
            else:
                print("Resuming: no valid features parsed from logs.")
        else:
            print("\nResuming: no dropped-feature logs found for this feature set. Starting fresh.")

    print(f"\nTotal features selected: {len(selected_feature_cols)}")  
    features_df = df_all[selected_feature_cols].copy()  
    print(f"Features DataFrame shape: {features_df.shape}")  
    print(f"Feature selection completed in {time.time() - feature_selection_start_time:.2f} seconds.")

    # ---------------------------------------------------------------------------  
    # Create the MODData Object  
    # ---------------------------------------------------------------------------  
    moddata_creation_start_time = time.time()
    targets = df_all['target']  
    structures = df_all[input_type]  
    structure_ids = df_all['material_id']  
    
    is_classification = not pd.api.types.is_numeric_dtype(targets) or targets.nunique() < 20  
    print(f"Determined task type: {'Classification' if is_classification else 'Regression'}")  
    
    num_classes = targets.nunique() if is_classification else 0  
    
    print("Creating MODData object...")  
    md = MODData(  
        materials=structures,  
        targets=targets,  
        num_classes={'target': num_classes},  
        structure_ids=structure_ids,  
        target_names=['target']  
    )  
    md.df_featurized = features_df  
    print(f"MODData object created with {md.df_featurized.shape[1]} features.")  
    
    print(f"MODData object creation completed in {time.time() - moddata_creation_start_time:.2f} seconds.")

    # ---------------------------------------------------------------------------  
    # Apply XGBoost preselection (now using the robust version)  
    # ---------------------------------------------------------------------------  
    print("\nApplying XGBoost robust preselection...")
    md = xgb_robust_preselection(md, feature_sets_str, n_jobs=24, target_threshold=800,   
                                    n_folds=5, consensus_threshold=0.6,   
                                    importance_percentile=10) # Added parameters for robust selection  
    
    # ---------------------------------------------------------------------------  
    # Save the MODData Object  
    # ---------------------------------------------------------------------------  
    save_moddata_start_time = time.time()
    output_folder = f'./{matbench_set}/precomputed'  
    os.makedirs(output_folder, exist_ok=True)  
    print(f"Ensured output directory exists: {output_folder}")
        
    output_filename = f'{output_folder}/{matbench_set}_{feature_sets_str}_featurizedMM2020Struct.pkl.gz'  
    md.save(output_filename)  
    print(f"MODData object saved to {output_filename}")  
    print(f"MODData object saving completed in {time.time() - save_moddata_start_time:.2f} seconds.")
    
    print(f"\n--- Main process finished in {time.time() - overall_start_time:.2f} seconds ---")

# -------------------------- Command-line Interface --------------------------  
if __name__ == "__main__":  
   parser = argparse.ArgumentParser(  
      description="Create and save MODData from a featurized CSV using specified matbench set "  
                  "and feature sets."  
   )  
   parser.add_argument("--matbench_set", required=True, help="Matbench set name (e.g., matbench_dielectric)")  
   parser.add_argument("--feature_sets", required=True,  
                     help="Space separated list of feature sets (e.g., \"matminer megnet_ofm mvl_all\")")  
   args = parser.parse_args()  
  
   # Split the feature_sets argument by whitespace  
   feature_sets_list = args.feature_sets.split()  
   main(matbench_set=args.matbench_set, feature_sets=feature_sets_list)