#!/usr/bin/env python
"""
Progress monitoring script for SOAP featurization workflow.
Provides real-time status updates and diagnostics.
"""

import os
import glob
import pandas as pd
import time
import argparse
from pathlib import Path


def check_chunks_status(chunks_dir="chunks"):
    """Check the status of data chunks."""
    print("=== Chunks Status ===")
    
    chunk_info_path = os.path.join(chunks_dir, "chunk_info.csv")
    if not os.path.exists(chunk_info_path):
        print("✗ No chunk_info.csv found. Run data preparation first.")
        return 0
    
    chunk_info = pd.read_csv(chunk_info_path)
    n_chunks = len(chunk_info)
    
    print(f"Total chunks: {n_chunks}")
    
    # Check if chunk files exist
    missing_chunks = []
    for _, row in chunk_info.iterrows():
        if not os.path.exists(row["path"]):
            missing_chunks.append(row["chunk_id"])
    
    if missing_chunks:
        print(f"✗ Missing chunk files: {missing_chunks}")
    else:
        print("✓ All chunk files present")
    
    return n_chunks


def check_features_status(features_dir="features", n_chunks=None):
    """Check the status of feature processing."""
    print("\n=== Features Status ===")
    
    if not os.path.exists(features_dir):
        print(f"✗ Features directory '{features_dir}' not found")
        return 0
    
    # Count completed feature files
    feature_files = glob.glob(os.path.join(features_dir, "*_features.csv"))
    n_completed = len(feature_files)
    
    print(f"Completed chunks: {n_completed}")
    
    if n_chunks:
        progress = (n_completed / n_chunks) * 100
        print(f"Progress: {progress:.1f}% ({n_completed}/{n_chunks})")
        
        if n_completed < n_chunks:
            missing_chunks = []
            for i in range(n_chunks):
                expected_file = os.path.join(features_dir, f"chunk_{i:04d}_features.csv")
                if not os.path.exists(expected_file):
                    missing_chunks.append(i)
            
            if len(missing_chunks) <= 10:
                print(f"Missing chunks: {missing_chunks}")
            else:
                print(f"Missing chunks: {missing_chunks[:10]}... (and {len(missing_chunks)-10} more)")
    
    return n_completed


def check_final_outputs():
    """Check the status of final output files."""
    print("\n=== Final Outputs Status ===")
    
    outputs = {
        "Combined SOAP features": "matbench_perovskites_soap_features.csv",
        "RFE-reduced features": "matbench_perovskites_soap_rfe.csv",
        "Selected features list": "matbench_perovskites_soap_rfe_selected_features.txt"
    }
    
    for name, filename in outputs.items():
        if os.path.exists(filename):
            if filename.endswith('.csv'):
                try:
                    df = pd.read_csv(filename)
                    print(f"✓ {name}: {df.shape}")
                except:
                    print(f"⚠ {name}: File exists but cannot read")
            else:
                print(f"✓ {name}: Present")
        else:
            print(f"✗ {name}: Not found")


def check_logs_status(logs_dir="logs"):
    """Check SLURM log files for errors."""
    print("\n=== Logs Status ===")
    
    if not os.path.exists(logs_dir):
        print(f"✗ Logs directory '{logs_dir}' not found")
        return
    
    # Check different types of log files
    log_patterns = {
        "Preparation logs": "prepare_data.*",
        "Array job logs": "soap_chunk_*.out",
        "Array job errors": "soap_chunk_*.err",
        "Combination logs": "combine_chunks.*"
    }
    
    for log_type, pattern in log_patterns.items():
        log_files = glob.glob(os.path.join(logs_dir, pattern))
        print(f"{log_type}: {len(log_files)} files")
        
        if log_type == "Array job errors" and log_files:
            # Check for non-empty error files
            error_files = []
            for log_file in log_files:
                if os.path.getsize(log_file) > 0:
                    error_files.append(log_file)
            
            if error_files:
                print(f"  ⚠ {len(error_files)} error files with content")
                if len(error_files) <= 5:
                    for error_file in error_files:
                        print(f"    - {error_file}")


def check_disk_usage():
    """Check disk usage of workflow directories."""
    print("\n=== Disk Usage ===")
    
    directories = ["chunks", "features", "logs"]
    
    for directory in directories:
        if os.path.exists(directory):
            # Calculate directory size
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
            
            # Convert to human readable format
            for unit in ['B', 'KB', 'MB', 'GB']:
                if total_size < 1024.0:
                    size_str = f"{total_size:.1f} {unit}"
                    break
                total_size /= 1024.0
            else:
                size_str = f"{total_size:.1f} TB"
            
            file_count = sum(len(files) for _, _, files in os.walk(directory))
            print(f"{directory}/: {size_str} ({file_count} files)")
        else:
            print(f"{directory}/: Not found")


def estimate_completion_time(n_completed, n_total, start_time=None):
    """Estimate completion time based on current progress."""
    if n_completed == 0 or n_total == 0:
        return "Unknown"
    
    if start_time is None:
        return "Unknown (no start time)"
    
    elapsed = time.time() - start_time
    rate = n_completed / elapsed  # chunks per second
    
    if rate == 0:
        return "Unknown"
    
    remaining = n_total - n_completed
    eta_seconds = remaining / rate
    
    # Convert to human readable format
    if eta_seconds < 60:
        return f"{eta_seconds:.0f} seconds"
    elif eta_seconds < 3600:
        return f"{eta_seconds/60:.0f} minutes"
    else:
        return f"{eta_seconds/3600:.1f} hours"


def monitor_continuous(interval=60):
    """Continuously monitor progress."""
    print("Starting continuous monitoring (Ctrl+C to stop)...")
    print(f"Update interval: {interval} seconds")
    
    try:
        while True:
            print(f"\n{'='*50}")
            print(f"Status Update: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print('='*50)
            
            n_chunks = check_chunks_status()
            n_completed = check_features_status(n_chunks=n_chunks)
            check_final_outputs()
            
            if n_chunks > 0:
                progress = (n_completed / n_chunks) * 100
                print(f"\nOverall Progress: {progress:.1f}% ({n_completed}/{n_chunks})")
            
            print(f"\nNext update in {interval} seconds...")
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user.")


def main():
    parser = argparse.ArgumentParser(description="Monitor SOAP featurization workflow progress")
    parser.add_argument("--continuous", action="store_true", 
                       help="Continuously monitor progress")
    parser.add_argument("--interval", type=int, default=60,
                       help="Update interval for continuous monitoring (seconds)")
    parser.add_argument("--chunks_dir", default="chunks",
                       help="Directory containing data chunks")
    parser.add_argument("--features_dir", default="features", 
                       help="Directory containing processed features")
    parser.add_argument("--logs_dir", default="logs",
                       help="Directory containing log files")
    
    args = parser.parse_args()
    
    if args.continuous:
        monitor_continuous(args.interval)
    else:
        print("=== SOAP Featurization Workflow Status ===")
        print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        n_chunks = check_chunks_status(args.chunks_dir)
        n_completed = check_features_status(args.features_dir, n_chunks)
        check_final_outputs()
        check_logs_status(args.logs_dir)
        check_disk_usage()
        
        if n_chunks > 0:
            progress = (n_completed / n_chunks) * 100
            print(f"\n=== Summary ===")
            print(f"Overall Progress: {progress:.1f}% ({n_completed}/{n_chunks})")
            
            if progress == 100:
                print("🎉 All chunks processed!")
            elif progress > 0:
                print("⏳ Processing in progress...")
            else:
                print("⚠ No chunks processed yet")


if __name__ == "__main__":
    main()
