#!/bin/bash
"""
Master workflow script for SOAP featurization of matbench_perovskites.
This script coordinates the entire process:
1. Data preparation and chunking
2. Parallel SOAP featurization
3. Chunk combination and RFE
"""

set -e  # Exit on any error

echo "=== SOAP Featurization Workflow for Matbench Perovskites ==="
echo "Starting at: $(date)"

# Step 1: Prepare data and create chunks
# echo ""
# echo "Step 1: Preparing dataset and creating chunks..."
# PREP_JOB=$(sbatch --parsable submit_prepare.sh)
# echo "Submitted data preparation job: $PREP_JOB"

# # Wait for preparation to complete
# echo "Waiting for data preparation to complete..."
# while squeue -j $PREP_JOB &> /dev/null; do
#     sleep 30
# done

# # Check if preparation was successful
# if [ ! -f "chunks/chunk_info.csv" ]; then
#     echo "ERROR: Data preparation failed. chunk_info.csv not found."
#     exit 1
# fi

# Get number of chunks
# N_CHUNKS=$(tail -n +2 chunks/chunk_info.csv | wc -l)
# echo "Found $N_CHUNKS chunks to process"

# # Update the array range in submit.sh
# sed -i "s/#SBATCH --array=.*/#SBATCH --array=0-$((N_CHUNKS-1))/" submit.sh
# echo "Updated submit.sh array range to 0-$((N_CHUNKS-1))"

# # Step 2: Submit array job for SOAP featurization
# echo ""
# echo "Step 2: Submitting SOAP featurization array job..."
# SOAP_JOB=$(sbatch --parsable submit.sh)
# echo "Submitted SOAP featurization array job: $SOAP_JOB"

# Wait for all array tasks to complete
echo "Waiting for SOAP featurization to complete..."
while squeue -j $SOAP_JOB &> /dev/null; do
    sleep 60
    # Show progress
    COMPLETED=$(ls features/*_features.csv 2>/dev/null | wc -l)
    echo "Progress: $COMPLETED/$N_CHUNKS chunks completed"
done

# Check if all chunks were processed
COMPLETED=$(ls features/*_features.csv 2>/dev/null | wc -l)
if [ $COMPLETED -ne $N_CHUNKS ]; then
    echo "WARNING: Only $COMPLETED out of $N_CHUNKS chunks were processed successfully"
    echo "Check the log files in logs/ directory for errors"
    echo "You may need to resubmit failed chunks manually"
fi

# Step 3: Combine chunks and perform RFE
echo ""
echo "Step 3: Combining chunks and performing RFE..."
COMBINE_JOB=$(sbatch --parsable submit_combine.sh)
echo "Submitted combination and RFE job: $COMBINE_JOB"

# Wait for combination to complete
echo "Waiting for chunk combination and RFE to complete..."
while squeue -j $COMBINE_JOB &> /dev/null; do
    sleep 30
done

# Check final results
echo ""
echo "=== Workflow Summary ==="
if [ -f "matbench_perovskites_soap_features.csv" ]; then
    echo "✓ Combined SOAP features file created: matbench_perovskites_soap_features.csv"
    N_SAMPLES=$(tail -n +2 matbench_perovskites_soap_features.csv | wc -l)
    N_FEATURES=$(head -n 1 matbench_perovskites_soap_features.csv | tr ',' '\n' | wc -l)
    echo "  - Samples: $N_SAMPLES"
    echo "  - Features: $N_FEATURES"
else
    echo "✗ Combined SOAP features file not found"
fi

if [ -f "matbench_perovskites_soap_rfe.csv" ]; then
    echo "✓ RFE-reduced features file created: matbench_perovskites_soap_rfe.csv"
    N_SAMPLES_RFE=$(tail -n +2 matbench_perovskites_soap_rfe.csv | wc -l)
    N_FEATURES_RFE=$(head -n 1 matbench_perovskites_soap_rfe.csv | tr ',' '\n' | wc -l)
    echo "  - Samples: $N_SAMPLES_RFE"
    echo "  - Features: $N_FEATURES_RFE"
else
    echo "✗ RFE-reduced features file not found"
fi

if [ -f "matbench_perovskites_soap_rfe_selected_features.txt" ]; then
    echo "✓ Selected features list created: matbench_perovskites_soap_rfe_selected_features.txt"
else
    echo "✗ Selected features list not found"
fi

echo ""
echo "Workflow completed at: $(date)"
echo ""
echo "Next steps:"
echo "1. Check log files in logs/ directory for any errors"
echo "2. Validate the final feature files"
echo "3. Use the RFE-reduced dataset for machine learning models"
