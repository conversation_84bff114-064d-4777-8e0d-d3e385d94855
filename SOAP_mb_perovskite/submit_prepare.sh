#!/bin/bash
#SBATCH --job-name=SOAP_prepare_data
#SBATCH --time=0-01:00:00
#SBATCH --output=logs/prepare_data.out
#SBATCH --error=logs/prepare_data.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --partition=keira
#SBATCH --mem-per-cpu=8000

# Create logs directory if it doesn't exist
mkdir -p logs

source ~/.bashrc
conda activate /globalscratch/users/r/g/rgouvea/test_env_modnet_pgnn/modnetpgnn_env

echo "Starting dataset preparation"
echo "Job ID: ${SLURM_JOB_ID}"
echo "Node: $(hostname)"
date

# Prepare the dataset and create chunks
python3 get_matbench_perovskites.py \
    --chunk_size 100 \
    --output_dir chunks \
    --save_full

echo "Dataset preparation completed"
date
