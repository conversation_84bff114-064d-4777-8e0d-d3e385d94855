#!/usr/bin/env python
"""
SOAP featurization script for matbench_perovskites dataset.
Uses the master SOAP configuration with mu1nu1 compression.
Supports chunk processing and result combination.
"""

import argparse
import os
import glob
import time
import pandas as pd
import numpy as np
import pickle
from pathlib import Path

# pymatgen and matminer imports
from pymatgen.core import Structure
from pymatgen.core.periodic_table import Element
from pymatgen.io.ase import AseAtomsAdaptor
from matminer.featurizers.structure import SiteStatsFingerprint

# dscribe SOAP (not matminer's wrapper)
from dscribe.descriptors import SOAP as DScribeSOAP

# sklearn for RFE
from sklearn.feature_selection import RFE
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler


# ----------------------------
# Master SOAP Configuration
# ----------------------------
MASTER_ELEMENTS = [Element.from_Z(Z).symbol for Z in range(1, 119)]
drop = {"He", "Ne", "Ar", "Kr", "Xe", "Rn", "Og"}
MASTER_ELEMENTS = [s for s in MASTER_ELEMENTS if s not in drop]

# Master SOAP parameters (from soap_featurization_example.py)
SOAP_CONFIG = {
    'r_cut': 5.0,
    'n_max': 6,
    'l_max': 6,
    'sigma': 0.5,
    'periodic': True,
    'compression': {"mode": "mu1nu1"},
    'dtype': "float32"
}

print(f"Using {len(MASTER_ELEMENTS)} master elements for SOAP featurization")


class DScribeSOAPWrapper:
    """Wrapper for dscribe SOAP to work with SiteStatsFingerprint."""
    
    def __init__(self, ds_soap, species):
        self.ds_soap = ds_soap
        self.species = list(species)  # preserve order for labeling
        self._nmax = ds_soap._n_max
        self._lmax = ds_soap._l_max
        self._S = len(self.species)
        # Feature count for mu1nu1 = S * n_max^2 * (l_max+1)
        self._nfeat = self._S * (self._nmax ** 2) * (self._lmax + 1)
        self._ase_adaptor = AseAtomsAdaptor()

    def featurize(self, structure, i):
        """Featurize a single site in a structure."""
        atoms = self._ase_adaptor.get_atoms(structure)
        # dscribe returns shape [n_centers, n_features] for centers=[i]
        x = self.ds_soap.create(atoms, centers=[i])
        return np.asarray(x[0], dtype=np.float32)

    def feature_labels(self):
        """Generate stable labels for SiteStatsFingerprint columns."""
        labels = []
        for z in self.species:
            for l in range(self._lmax + 1):
                for n in range(1, self._nmax + 1):
                    for np_ in range(1, self._nmax + 1):
                        labels.append(f"SOAP_mu1nu1[Z={z}][l={l}][n={n}][np={np_}]")
        return labels


def initialize_soap_featurizer():
    """Initialize the SOAP featurizer with master configuration."""
    print("Initializing SOAP featurizer with master configuration...")
    print(f"SOAP parameters: {SOAP_CONFIG}")
    
    # Initialize dscribe SOAP
    soap_ds = DScribeSOAP(
        species=MASTER_ELEMENTS,
        **SOAP_CONFIG
    )
    
    # Wrap for matminer compatibility
    soap_wrapper = DScribeSOAPWrapper(soap_ds, MASTER_ELEMENTS)
    
    # Create structure-level aggregator
    ssf = SiteStatsFingerprint(soap_wrapper, stats=("mean", "std_dev"))
    
    print(f"SOAP featurizer initialized. Expected feature count: {soap_wrapper._nfeat * 2}")  # *2 for mean + std_dev
    
    return ssf, soap_wrapper


def featurize_structures(structures, material_ids, ssf):
    """Featurize a list of structures using SOAP."""
    print(f"Featurizing {len(structures)} structures...")
    
    # Create DataFrame for featurization
    df = pd.DataFrame({
        "material_id": material_ids,
        "structure": structures
    })
    
    # Time the featurization
    start_time = time.time()
    
    try:
        # Featurize using SiteStatsFingerprint
        df_feat = ssf.featurize_dataframe(df, col_id="structure")
        
        # Extract SOAP columns
        soap_cols = [c for c in df_feat.columns if "SOAP" in c]
        result_df = df_feat[["material_id"] + soap_cols].copy()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"Featurization completed in {total_time:.2f} seconds")
        print(f"Average time per structure: {total_time/len(structures)*1000:.2f} ms")
        print(f"Final feature matrix shape: {result_df.shape}")
        
        return result_df
        
    except Exception as e:
        print(f"Error during featurization: {e}")
        raise


def process_chunk(chunk_path, output_dir, ssf):
    """Process a single chunk of data."""
    print(f"\nProcessing chunk: {chunk_path}")
    
    # Load chunk
    chunk_df = pd.read_pickle(chunk_path)
    print(f"Loaded chunk with {len(chunk_df)} samples")
    
    # Extract structures and IDs
    structures = chunk_df["structure"].tolist()
    material_ids = chunk_df["material_id"].tolist()
    targets = chunk_df["e_form"].tolist() if "e_form" in chunk_df.columns else None
    
    # Featurize
    features_df = featurize_structures(structures, material_ids, ssf)
    
    # Add target if available
    if targets is not None:
        features_df["e_form"] = targets
    
    # Save result
    chunk_name = Path(chunk_path).stem
    output_path = os.path.join(output_dir, f"{chunk_name}_features.csv")
    features_df.to_csv(output_path, index=False)
    
    print(f"Saved features to: {output_path}")
    return output_path


def combine_chunks(chunks_dir, output_path="matbench_perovskites_soap_features.csv"):
    """Combine processed chunks into a single CSV file."""
    print(f"\nCombining chunks from directory: {chunks_dir}")
    
    # Find all feature files
    feature_files = glob.glob(os.path.join(chunks_dir, "*_features.csv"))
    
    if not feature_files:
        raise ValueError(f"No feature files found in {chunks_dir}")
    
    print(f"Found {len(feature_files)} feature files to combine")
    
    # Load and combine all chunks
    dfs = []
    for file_path in sorted(feature_files):
        print(f"Loading: {file_path}")
        df = pd.read_csv(file_path)
        dfs.append(df)
    
    # Combine all dataframes
    combined_df = pd.concat(dfs, ignore_index=True)
    
    print(f"Combined dataset shape: {combined_df.shape}")
    print(f"Total samples: {len(combined_df)}")
    
    # Save combined result
    combined_df.to_csv(output_path, index=False)
    print(f"Saved combined features to: {output_path}")
    
    return combined_df


def perform_rfe(df, target_col="e_form", n_features=300, output_path=None):
    """Perform Recursive Feature Elimination on SOAP features."""
    print(f"\nPerforming RFE to select {n_features} features...")
    
    # Separate features and target
    feature_cols = [c for c in df.columns if c.startswith("SOAP")]
    if target_col not in df.columns:
        raise ValueError(f"Target column '{target_col}' not found in dataframe")
    
    X = df[feature_cols].copy()
    y = df[target_col].copy()
    
    print(f"Initial feature count: {len(feature_cols)}")
    print(f"Target samples: {len(y)}")
    
    # Handle missing values
    X = X.fillna(X.mean())
    y = y.dropna()
    X = X.loc[y.index]  # Align with non-NaN targets
    
    print(f"After removing NaN targets: {len(y)} samples")
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Initialize RFE with RandomForest
    estimator = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    rfe = RFE(estimator=estimator, n_features_to_select=n_features, step=0.1)
    
    print("Running RFE... This may take a while.")
    start_time = time.time()
    
    rfe.fit(X_scaled, y)
    
    end_time = time.time()
    print(f"RFE completed in {end_time - start_time:.2f} seconds")
    
    # Get selected features
    selected_features = X.columns[rfe.support_].tolist()
    print(f"Selected {len(selected_features)} features")
    
    # Create reduced dataset
    reduced_df = df[["material_id"] + selected_features + [target_col]].copy()
    
    if output_path:
        reduced_df.to_csv(output_path, index=False)
        print(f"Saved RFE-reduced dataset to: {output_path}")
        
        # Save selected feature names
        feature_list_path = output_path.replace(".csv", "_selected_features.txt")
        with open(feature_list_path, "w") as f:
            for feat in selected_features:
                f.write(f"{feat}\n")
        print(f"Saved selected feature list to: {feature_list_path}")
    
    return reduced_df, selected_features


def main():
    parser = argparse.ArgumentParser(description="SOAP featurization for matbench_perovskites")
    parser.add_argument("--chunk_id", type=int, help="Process specific chunk ID (for array jobs)")
    parser.add_argument("--chunks_dir", type=str, default="chunks", 
                       help="Directory containing data chunks")
    parser.add_argument("--output_dir", type=str, default="features",
                       help="Output directory for features")
    parser.add_argument("--combine_chunks", type=str, 
                       help="Combine chunks from specified directory")
    parser.add_argument("--rfe", action="store_true",
                       help="Perform RFE feature selection")
    parser.add_argument("--rfe_features", type=int, default=300,
                       help="Number of features to select with RFE")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    if args.combine_chunks:
        # Combine chunks mode
        combined_df = combine_chunks(args.combine_chunks)
        
        if args.rfe:
            print("\nPerforming RFE on combined dataset...")
            rfe_output = "matbench_perovskites_soap_rfe.csv"
            perform_rfe(combined_df, n_features=args.rfe_features, output_path=rfe_output)
            
    elif args.chunk_id is not None:
        # Process specific chunk (for array jobs)
        chunk_info_path = os.path.join(args.chunks_dir, "chunk_info.csv")
        if not os.path.exists(chunk_info_path):
            raise FileNotFoundError(f"Chunk info file not found: {chunk_info_path}")
        
        chunk_info = pd.read_csv(chunk_info_path)
        if args.chunk_id >= len(chunk_info):
            raise ValueError(f"Chunk ID {args.chunk_id} out of range (0-{len(chunk_info)-1})")
        
        chunk_path = chunk_info.iloc[args.chunk_id]["path"]
        
        # Initialize SOAP featurizer
        ssf, _ = initialize_soap_featurizer()
        
        # Process chunk
        process_chunk(chunk_path, args.output_dir, ssf)
        
    else:
        # Process all chunks sequentially (for testing)
        chunk_info_path = os.path.join(args.chunks_dir, "chunk_info.csv")
        if not os.path.exists(chunk_info_path):
            raise FileNotFoundError(f"Chunk info file not found: {chunk_info_path}")
        
        chunk_info = pd.read_csv(chunk_info_path)
        
        # Initialize SOAP featurizer
        ssf, _ = initialize_soap_featurizer()
        
        # Process all chunks
        for _, row in chunk_info.iterrows():
            process_chunk(row["path"], args.output_dir, ssf)


if __name__ == "__main__":
    main()
